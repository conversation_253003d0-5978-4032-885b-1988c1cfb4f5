name: Dependency Review

on:
  pull_request:
    branches: [ main ]

permissions:
  contents: read

jobs:
  dependency-review:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      
      - name: Dependency Review
        # This action is provided by GitHub and checks against the GitHub Advisory Database
        uses: actions/dependency-review-action@v4