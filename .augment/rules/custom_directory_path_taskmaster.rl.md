---
type: "agent_requested"
description: "Enforce a project-specific, dedicated directory for TaskMaster AI tasks artifacts to ensure clean project structure and avoid conflicts."
---
# 📚 Rule: Custom Directory Paths for persisting the TaskMaster AI's artifacts

## Core Principle
**ALL TaskMaster AI operations MUST use a single, dedicated directory for persisting and reading all tasks-related artifacts.** This directory must be configurable and must override the default `'.taskmaster'` directory to centralize all tasks management files.

---

## Dependencies

Please refer to the ".ai_framework" config file to understand where are the new paths for the TaskMaster AI's operations and artifacts.

## Mandatory Rules

### 1. Dedicated Root Directory
All TaskMaster AI-related task-related files generated/updated such as the main task file, tasks-breakdown, tasks complexity report, and TaskMaster AI Models configuration, must be stored within a single, specified directory at the project root.

- The new directory location must be readed from the ai framework's main configuration file (a custom `config.json` file at `.ai_framework/` directory).
- The default TaskMaster directory (`.taskmaster`) is strictly forbidden once this rule is in effect.

### 2. File Organization
Once this rule is in effect, all TaskMaster operations should use the structured sub-directory layout described below to maintain clarity:
- **`<ai_framework_project_management_directory>/prds/`**: This subdirectory will be used generating/storing/parsing the PRDs, e.g., `prd.json`.
- **`<ai_framework_project_management_directory>/tasks/`**: This subdirectory will store the main task list, e.g., `tasks.json`, the individual tasks resulting from a tasks-breakdown, e.g., `task_001.txt`, and also the tasks complexity report,  e.g., `task-complexity-report.json`.
- **`<ai_framework_project_management_directory>/integrations/taskmaster_ai/`**: This subdirectory will store the core files for Taskmaster(`config.json`, and `state.json`).

### 3. Configuration Overwrite
The AI agent must recognize and use the new directories described above. This requires an initial check in the project configuration before any TaskMaster operation is performed.
The AI agent's internal logic must parse this configuration and set the new directories as the new root for all TaskMaster-related file I/O.

